# Cross-Device Data Sync Setup Guide

Your donation checklist application now supports cross-device data synchronization! This means changes made on one device will be visible to all users on any device when they log in.

## Current Status
- ✅ **Local Storage**: Works on same device/browser
- ⚙️ **Cloud Sync**: Requires setup (instructions below)

## Quick Setup (5 minutes)

### Step 1: Create a Free JSONBin.io Account
1. Go to [https://jsonbin.io](https://jsonbin.io)
2. Click "Sign Up" and create a free account
3. Verify your email address

### Step 2: Create a New Bin
1. After logging in, click "Create Bin"
2. Name it something like "donation-checklist-data"
3. Leave the content as `{}` (empty JSON object)
4. Click "Create"
5. **Copy the Bin ID** from the URL (it looks like: `676c8e5ead19ca34f8d4f8a2`)

### Step 3: Get Your API Key
1. Go to your JSONBin.io dashboard
2. Click on "API Keys" in the sidebar
3. **Copy your Master Key** (starts with `$2a$10$...`)

### Step 4: Update Your Code
1. Open `index.html` in a text editor
2. Find these lines (around line 602-603):
   ```javascript
   const BIN_ID = "YOUR_BIN_ID_HERE"; // Replace with your JSONBin.io bin ID
   const API_KEY = "YOUR_API_KEY_HERE"; // Replace with your JSONBin.io API key
   ```
3. Replace `YOUR_BIN_ID_HERE` with your actual Bin ID
4. Replace `YOUR_API_KEY_HERE` with your actual API key
5. Save the file

### Example:
```javascript
const BIN_ID = "676c8e5ead19ca34f8d4f8a2"; // Your actual bin ID
const API_KEY = "$2a$10$abcdef1234567890abcdef1234567890abcdef12"; // Your actual API key
```

## How It Works

### Without Cloud Sync (Current)
- ✅ Data saves locally on each device
- ❌ Changes don't sync between devices
- ✅ Works offline

### With Cloud Sync (After Setup)
- ✅ Data saves to cloud database
- ✅ Changes sync across all devices instantly
- ✅ Works from anywhere with internet
- ✅ Local backup still maintained

## Features After Setup

1. **Real-time Sync**: Changes made on any device appear on all other devices
2. **Notifications**: Visual feedback when data syncs successfully
3. **Fallback**: If cloud is unavailable, uses local storage
4. **Cross-platform**: Works on phones, tablets, computers

## Security Notes

- Your API key allows access to your data bin
- Keep your API key private (don't share publicly)
- JSONBin.io free tier includes 10,000 requests/month (more than enough)
- Data is stored securely on JSONBin.io servers

## Troubleshooting

### "Using local storage only" message
- Check that you've replaced both BIN_ID and API_KEY with actual values
- Ensure there are no typos in the credentials

### Data not syncing
- Check browser console for error messages
- Verify internet connection
- Confirm API key is valid in JSONBin.io dashboard

### Need Help?
- Check the browser console (F12) for detailed error messages
- Verify your JSONBin.io account is active
- Test the API key in JSONBin.io dashboard

## Cost
- **JSONBin.io Free Tier**: 10,000 API calls/month (FREE)
- **Estimated Usage**: ~50-100 calls/day for normal use
- **Upgrade**: Only needed for very heavy usage

---

**Ready to enable cross-device sync?** Follow the 4 steps above and your donation checklist will work across all devices! 🚀
