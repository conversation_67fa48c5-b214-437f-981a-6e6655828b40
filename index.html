<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Donation Drive Checklist</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Inter", sans-serif;
        background-color: #f8fafc; /* slate-50 */
      }
      .main-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 1rem;
      }
      .table-container {
        max-width: 1400px;
        width: 100%;
        margin: 2rem auto;
        padding: 1rem;
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);
      }
      @media (min-width: 640px) {
        .table-container {
          padding: 2rem;
        }
      }
      .notes-input,
      .styled-select {
        width: 100%;
        border: 1px solid #cbd5e1; /* slate-300 */
        border-radius: 0.375rem;
        padding: 0.25rem 0.5rem;
        transition: border-color 0.2s;
        background-color: #fff;
      }
      .notes-input:focus,
      .styled-select:focus {
        outline: none;
        border-color: #4f46e5; /* indigo-600 */
      }
      .add-product-form {
        margin-top: 1rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      @media (min-width: 640px) {
        .add-product-form {
          flex-direction: row;
        }
      }
      .action-btn {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        transition: background-color 0.2s;
        white-space: nowrap;
      }
      .edit-btn {
        background-color: #e0e7ff;
        color: #4338ca;
      }
      .edit-btn:hover {
        background-color: #c7d2fe;
      }
      .delete-btn {
        background-color: #fee2e2;
        color: #b91c1c;
      }
      .delete-btn:hover {
        background-color: #fecaca;
      }
      .save-btn {
        background-color: #dcfce7;
        color: #166534;
      }
      .save-btn:hover {
        background-color: #bbf7d0;
      }

      .progress-bar-container {
        background-color: #e5e7eb;
        border-radius: 0.5rem;
        overflow: hidden;
        margin-top: 0.5rem;
      }
      .progress-bar {
        background-color: #4f46e5;
        height: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.75rem;
        font-weight: 500;
        transition: width 0.5s ease-in-out;
      }

      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 50;
      }
      .modal-box {
        background: white;
        padding: 2rem;
        border-radius: 0.5rem;
        width: 90%;
        max-width: 400px;
        text-align: center;
      }

      /* Table styles for all screen sizes - horizontal scroll on mobile */
      .responsive-table {
        min-width: 800px; /* Ensure table has minimum width for proper display */
      }

      /* Mobile adjustments while keeping table format */
      @media (max-width: 1023px) {
        .table-container {
          padding: 1rem 0.5rem;
        }

        .responsive-table th,
        .responsive-table td {
          padding: 0.5rem 0.25rem;
          font-size: 0.875rem;
        }

        .notes-input,
        .styled-select {
          font-size: 0.875rem;
          padding: 0.25rem;
        }

        .action-btn {
          padding: 0.25rem 0.375rem;
          font-size: 0.625rem;
        }
      }

      /* Brand header styles */
      .brand-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        padding: 1rem;
        font-weight: 600;
        font-size: 1.125rem;
        letter-spacing: 0.025em;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      /* Add top padding to body to account for fixed header */
      body {
        padding-top: 4rem; /* Adjust based on header height */
      }

      @media (min-width: 640px) {
        .brand-header {
          font-size: 1.25rem;
          padding: 1.5rem;
        }
        body {
          padding-top: 5rem; /* Larger padding for larger header */
        }
      }

      /* Cost total styles */
      .cost-total-container {
        background-color: #f1f5f9;
        border: 1px solid #cbd5e1;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .cost-total-label {
        font-weight: 500;
        color: #475569;
        font-size: 0.875rem;
      }
      .cost-total-amount {
        font-weight: 600;
        color: #1e293b;
        font-size: 1rem;
      }
      .grand-total-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      }
      .grand-total-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      .grand-total-amount {
        font-size: 2rem;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
      @media (max-width: 640px) {
        .grand-total-title {
          font-size: 1.125rem;
        }
        .grand-total-amount {
          font-size: 1.75rem;
        }
        .cost-total-container {
          padding: 0.5rem;
        }
      }

      /* Print Styles */
      /* Footer styles */
      .footer {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        padding: 2rem 1rem;
        margin-top: 3rem;
      }

      .footer p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      @media (max-width: 640px) {
        .footer {
          padding: 1.5rem 0.5rem;
        }
        .footer p {
          font-size: 0.625rem !important;
          white-space: nowrap;
        }
      }

      .footer h3 {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: white;
      }

      .footer p {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
      }

      .footer .contact-info {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 1.5rem;
      }

      @media (min-width: 768px) {
        .footer .contact-info {
          flex-direction: row;
          justify-content: center;
          gap: 3rem;
        }
      }

      .footer .organization {
        margin-bottom: 1rem;
      }

      .footer .organization h4 {
        font-weight: 600;
        color: #94a3b8;
        margin-bottom: 0.25rem;
      }

      @media print {
        body * {
          visibility: hidden;
        }
        #checklist-container,
        #checklist-container * {
          visibility: visible;
        }
        #checklist-container {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }
        .no-print {
          display: none !important;
        }
        .table-container {
          box-shadow: none;
          border: 1px solid #ccc;
        }
        .notes-input,
        .styled-select {
          border: none;
          background: transparent;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }
        input[type="checkbox"] {
          border: 1px solid #000;
        }
        .brand-header {
          background: #667eea !important;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .footer {
          display: none !important;
        }
        .grand-total-container,
        .cost-total-container {
          display: none !important;
        }
      }
    </style>
  </head>
  <body>
    <!-- Brand Header -->
    <div class="brand-header">Christine, BB, Gideon, Nathaniel's Project</div>

    <div class="main-container">
      <!-- Login Form Container -->
      <div
        id="login-container"
        class="w-full max-w-sm p-8 space-y-6 bg-white rounded-lg shadow-md"
      >
        <h2 class="text-2xl font-bold text-center text-slate-800">
          Login to View Checklist
        </h2>
        <div>
          <label for="username" class="text-sm font-medium text-slate-700"
            >Username</label
          >
          <input
            type="text"
            id="username"
            name="username"
            class="mt-1 block w-full px-3 py-2 bg-white border border-slate-300 rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="homeless"
          />
        </div>
        <div>
          <label for="password" class="text-sm font-medium text-slate-700"
            >Password</label
          >
          <input
            type="password"
            id="password"
            name="password"
            class="mt-1 block w-full px-3 py-2 bg-white border border-slate-300 rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="homeless"
          />
        </div>
        <p id="error-message" class="text-sm text-red-500 text-center hidden">
          Invalid username or password.
        </p>
        <button
          id="login-button"
          class="w-full py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Login
        </button>
      </div>

      <!-- Checklist Container (Initially Hidden) -->
      <div id="checklist-container" class="table-container hidden">
        <div
          class="flex flex-col sm:flex-row justify-between sm:items-center mb-6 no-print"
        >
          <h1
            class="text-2xl lg:text-3xl font-bold text-slate-800 mb-4 sm:mb-0"
          >
            Donation Checklist
          </h1>
          <div class="flex flex-col sm:flex-row gap-2">
            <button
              id="print-btn"
              class="w-full sm:w-auto py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-md shadow-sm"
            >
              Print View
            </button>
            <button
              id="logout-btn"
              class="w-full sm:w-auto py-2 px-4 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-md shadow-sm"
            >
              Logout
            </button>
          </div>
        </div>

        <!-- Grand Total Section -->
        <div class="grand-total-container">
          <div class="grand-total-title">Total Budget Needed</div>
          <div id="grand-total-amount" class="grand-total-amount">$0.00</div>
        </div>

        <!-- Denver Rescue Mission Section -->
        <div>
          <div class="mb-4">
            <h2 class="text-xl font-semibold text-slate-800">
              Denver Rescue Mission (Men's Items)
            </h2>
            <p class="text-sm text-slate-600">
              Address: 6090 Smith Rd, Denver | Phone: (*************
            </p>
            <div class="progress-bar-container">
              <div id="drm-progress" class="progress-bar" style="width: 0%">
                0%
              </div>
            </div>
            <div class="cost-total-container">
              <span class="cost-total-label">Estimated Total Cost:</span>
              <span id="drm-cost-total" class="cost-total-amount">$0.00</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table
              class="w-full text-sm text-left text-slate-600 responsive-table"
            >
              <thead>
                <tr>
                  <th class="px-2 py-3">Product</th>
                  <th class="px-2 py-3 text-center">Qty</th>
                  <th class="px-2 py-3">Priority</th>
                  <th class="px-2 py-3">Est. Cost</th>
                  <th class="px-2 py-3">Needed By</th>
                  <th class="px-2 py-3 text-center">Bought</th>
                  <th class="px-2 py-3 text-center">Ready</th>
                  <th class="px-2 py-3 text-center">Donated</th>
                  <th class="px-2 py-3">Notes</th>
                  <th class="px-2 py-3 text-center no-print">Actions</th>
                </tr>
              </thead>
              <tbody id="drm-table-body"></tbody>
            </table>
          </div>
          <div class="add-product-form no-print">
            <input
              type="text"
              id="new-product-drm"
              class="notes-input"
              placeholder="Enter new product name..."
            />
            <button
              id="add-product-drm-btn"
              class="w-full sm:w-auto py-2 px-4 bg-indigo-500 hover:bg-indigo-600 text-white font-semibold rounded-md shadow-sm"
            >
              Add
            </button>
          </div>
        </div>

        <!-- Samaritan House Section -->
        <div class="mt-12">
          <div class="mb-4">
            <h2 class="text-xl font-semibold text-slate-800">
              Samaritan House (Women's Items)
            </h2>
            <p class="text-sm text-slate-600">
              Address: 2301 Lawrence St, Denver | Phone: (*************
            </p>
            <div class="progress-bar-container">
              <div id="sh-progress" class="progress-bar" style="width: 0%">
                0%
              </div>
            </div>
            <div class="cost-total-container">
              <span class="cost-total-label">Estimated Total Cost:</span>
              <span id="sh-cost-total" class="cost-total-amount">$0.00</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table
              class="w-full text-sm text-left text-slate-600 responsive-table"
            >
              <thead>
                <tr>
                  <th class="px-2 py-3">Product</th>
                  <th class="px-2 py-3 text-center">Qty</th>
                  <th class="px-2 py-3">Priority</th>
                  <th class="px-2 py-3">Est. Cost</th>
                  <th class="px-2 py-3">Needed By</th>
                  <th class="px-2 py-3 text-center">Bought</th>
                  <th class="px-2 py-3 text-center">Ready</th>
                  <th class="px-2 py-3 text-center">Donated</th>
                  <th class="px-2 py-3">Notes</th>
                  <th class="px-2 py-3 text-center no-print">Actions</th>
                </tr>
              </thead>
              <tbody id="sh-table-body"></tbody>
            </table>
          </div>
          <div class="add-product-form no-print">
            <input
              type="text"
              id="new-product-sh"
              class="notes-input"
              placeholder="Enter new product name..."
            />
            <button
              id="add-product-sh-btn"
              class="w-full sm:w-auto py-2 px-4 bg-indigo-500 hover:bg-indigo-600 text-white font-semibold rounded-md shadow-sm"
            >
              Add
            </button>
          </div>
        </div>

        <!-- Food Donations Section -->
        <div class="mt-12">
          <div class="mb-4">
            <h2 class="text-xl font-semibold text-slate-800">
              Food Donations (General) and Others
            </h2>
            <p class="text-sm text-slate-600">
              For general food bank distribution and other miscellaneous items.
            </p>
            <div class="progress-bar-container">
              <div id="food-progress" class="progress-bar" style="width: 0%">
                0%
              </div>
            </div>
            <div class="cost-total-container">
              <span class="cost-total-label">Estimated Total Cost:</span>
              <span id="food-cost-total" class="cost-total-amount">$0.00</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table
              class="w-full text-sm text-left text-slate-600 responsive-table"
            >
              <thead>
                <tr>
                  <th class="px-2 py-3">Product</th>
                  <th class="px-2 py-3 text-center">Qty</th>
                  <th class="px-2 py-3">Priority</th>
                  <th class="px-2 py-3">Est. Cost</th>
                  <th class="px-2 py-3">Needed By</th>
                  <th class="px-2 py-3 text-center">Bought</th>
                  <th class="px-2 py-3 text-center">Ready</th>
                  <th class="px-2 py-3 text-center">Donated</th>
                  <th class="px-2 py-3">Notes</th>
                  <th class="px-2 py-3 text-center no-print">Actions</th>
                </tr>
              </thead>
              <tbody id="food-table-body"></tbody>
            </table>
          </div>
          <div class="add-product-form no-print">
            <input
              type="text"
              id="new-product-food"
              class="notes-input"
              placeholder="Enter new food item or other donation..."
            />
            <button
              id="add-product-food-btn"
              class="w-full sm:w-auto py-2 px-4 bg-indigo-500 hover:bg-indigo-600 text-white font-semibold rounded-md shadow-sm"
            >
              Add
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="footer no-print">
      <div class="max-w-4xl mx-auto">
        <p style="font-size: 0.75rem; color: rgba(255, 255, 255, 0.8)">
          © <span id="current-year"></span> Christine, BB, Gideon, Nathaniel's
          Project | Proverbs 19 vrs 17
        </p>
      </div>
    </footer>

    <!-- Confirmation Modal -->
    <div id="delete-modal" class="modal-overlay hidden">
      <div class="modal-box">
        <h3 class="text-lg font-bold text-slate-800">Confirm Deletion</h3>
        <p class="text-sm text-slate-600 mt-2 mb-4">
          Are you sure you want to delete this item?
        </p>
        <div class="flex justify-center gap-4">
          <button
            id="cancel-delete-btn"
            class="py-2 px-4 bg-slate-200 hover:bg-slate-300 text-slate-800 font-semibold rounded-md"
          >
            Cancel
          </button>
          <button
            id="confirm-delete-btn"
            class="py-2 px-4 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-md"
          >
            Delete
          </button>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        // Set dynamic year in footer
        document.getElementById("current-year").textContent =
          new Date().getFullYear();

        // --- DOM Elements ---
        const loginContainer = document.getElementById("login-container");
        const checklistContainer = document.getElementById(
          "checklist-container"
        );
        const loginButton = document.getElementById("login-button");
        const usernameInput = document.getElementById("username");
        const passwordInput = document.getElementById("password");
        const errorMessage = document.getElementById("error-message");
        const deleteModal = document.getElementById("delete-modal");
        const confirmDeleteBtn = document.getElementById("confirm-delete-btn");
        const cancelDeleteBtn = document.getElementById("cancel-delete-btn");
        const printBtn = document.getElementById("print-btn");
        const logoutBtn = document.getElementById("logout-btn");

        const drmTableBody = document.getElementById("drm-table-body");
        const newProductDrmInput = document.getElementById("new-product-drm");
        const addProductDrmBtn = document.getElementById("add-product-drm-btn");

        const shTableBody = document.getElementById("sh-table-body");
        const newProductShInput = document.getElementById("new-product-sh");
        const addProductShBtn = document.getElementById("add-product-sh-btn");

        const foodTableBody = document.getElementById("food-table-body");
        const newProductFoodInput = document.getElementById("new-product-food");
        const addProductFoodBtn = document.getElementById(
          "add-product-food-btn"
        );

        let rowToDelete = null;

        // --- Data Persistence Functions ---
        // To enable cross-device sync, you need to:
        // 1. Go to https://jsonbin.io and create a free account
        // 2. Create a new bin and get the bin ID
        // 3. Get your API key from the dashboard
        // 4. Replace the values below with your actual bin ID and API key

        const BIN_ID = "6862169e8a456b7966b876ea"; // Your JSONBin.io bin ID
        const API_KEY =
          "$2a$10$8B1gnNud1H0P7S8EmtTM4ez8BNqFf/yXVkz0goZk.kRUzv34sRlq6"; // Your JSONBin.io API key
        const API_URL = `https://api.jsonbin.io/v3/b/${BIN_ID}`;

        async function saveData() {
          const data = {
            drm: extractTableData("drm-table-body"),
            sh: extractTableData("sh-table-body"),
            food: extractTableData("food-table-body"),
            lastUpdated: new Date().toISOString(),
          };

          // Check if API credentials are configured
          if (
            BIN_ID !== "YOUR_BIN_ID_HERE" &&
            API_KEY !== "YOUR_API_KEY_HERE"
          ) {
            try {
              // Save to cloud database
              const response = await fetch(API_URL, {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                  "X-Master-Key": API_KEY,
                },
                body: JSON.stringify(data),
              });

              if (response.ok) {
                console.log(
                  "✅ Data saved to cloud successfully - visible across all devices!"
                );
                showNotification("Data synced across all devices!", "success");
              } else {
                throw new Error("Failed to save to cloud");
              }
            } catch (error) {
              console.error("❌ Error saving to cloud:", error);
              showNotification(
                "Using local storage only - set up cloud sync for cross-device sharing",
                "warning"
              );
            }
          } else {
            console.log(
              "📱 Using local storage only - configure API credentials for cross-device sync"
            );
            showNotification("Using local storage only", "info");
          }

          // Always save locally as backup
          localStorage.setItem("donationChecklistData", JSON.stringify(data));
        }

        async function loadData() {
          // Check if API credentials are configured
          if (
            BIN_ID !== "YOUR_BIN_ID_HERE" &&
            API_KEY !== "YOUR_API_KEY_HERE"
          ) {
            try {
              // Try to load from cloud database first
              const response = await fetch(API_URL + "/latest", {
                method: "GET",
                headers: {
                  "X-Master-Key": API_KEY,
                },
              });

              if (response.ok) {
                const result = await response.json();
                console.log("✅ Data loaded from cloud successfully");
                showNotification("Data synced from cloud!", "success");
                return result.record;
              }
            } catch (error) {
              console.error("❌ Error loading from cloud:", error);
            }
          }

          // Fallback to localStorage
          const savedData = localStorage.getItem("donationChecklistData");
          if (savedData) {
            console.log("📱 Data loaded from local storage");
            return JSON.parse(savedData);
          }

          return null;
        }

        // Notification system
        function showNotification(message, type = "info") {
          // Create notification element if it doesn't exist
          let notification = document.getElementById("notification");
          if (!notification) {
            notification = document.createElement("div");
            notification.id = "notification";
            notification.style.cssText = `
              position: fixed;
              top: 80px;
              right: 20px;
              padding: 12px 20px;
              border-radius: 6px;
              color: white;
              font-size: 14px;
              font-weight: 500;
              z-index: 1000;
              opacity: 0;
              transition: opacity 0.3s ease;
              max-width: 300px;
            `;
            document.body.appendChild(notification);
          }

          // Set color based on type
          const colors = {
            success: "#10b981",
            warning: "#f59e0b",
            error: "#ef4444",
            info: "#3b82f6",
          };

          notification.style.backgroundColor = colors[type] || colors.info;
          notification.textContent = message;
          notification.style.opacity = "1";

          // Hide after 3 seconds
          setTimeout(() => {
            notification.style.opacity = "0";
          }, 3000);
        }

        function extractTableData(tableBodyId) {
          const tableBody = document.getElementById(tableBodyId);
          const rows = [];
          for (let row of tableBody.rows) {
            const rowData = {
              name: row.querySelector('[data-field="name"]').textContent,
              quantity: row.querySelector('[data-field="quantity"]').value,
              priority: row.querySelector('[data-field="priority"]').value,
              cost: row.querySelector('[data-field="cost"]').value,
              date: row.querySelector('[data-field="date"]').value,
              bought: row.querySelector('[data-field="bought"]').checked,
              ready: row.querySelector('[data-field="ready"]').checked,
              donated: row.querySelector('[data-field="donated"]').checked,
              notes: row.querySelector('[data-field="notes"]').value,
            };
            rows.push(rowData);
          }
          return rows;
        }

        function populateTableFromData(tableBodyId, data) {
          const tableBody = document.getElementById(tableBodyId);
          tableBody.innerHTML = ""; // Clear existing rows

          data.forEach((rowData) => {
            const newRow = tableBody.insertRow();
            newRow.className =
              "bg-white border-b last:border-b-0 hover:bg-slate-50";
            newRow.innerHTML = createRowHTML(rowData.name);

            // Populate the data
            newRow.querySelector('[data-field="quantity"]').value =
              rowData.quantity || "";
            newRow.querySelector('[data-field="priority"]').value =
              rowData.priority || "Normal";
            newRow.querySelector('[data-field="cost"]').value =
              rowData.cost || "";
            newRow.querySelector('[data-field="date"]').value =
              rowData.date || "";
            newRow.querySelector('[data-field="bought"]').checked =
              rowData.bought || false;
            newRow.querySelector('[data-field="ready"]').checked =
              rowData.ready || false;
            newRow.querySelector('[data-field="donated"]').checked =
              rowData.donated || false;
            newRow.querySelector('[data-field="notes"]').value =
              rowData.notes || "";
          });
        }

        // --- Initial Data ---
        const initialDrmProducts = [
          "Toothpaste",
          "Mouthwash",
          "Bars of Soap",
          "Lotion",
          "Deodorant",
        ];
        const initialShProducts = [
          "Socks",
          "Shampoo",
          "Conditioner",
          "General Hygiene Products",
          "Pads/Tampons (Sealed)",
          "Crossword Puzzles/Coloring Books",
        ];
        const initialFoodProducts = [
          "Canned Vegetables",
          "Canned Fruit",
          "Peanut Butter",
          "Pasta",
          "Rice",
          "Cereal",
        ];

        function attemptLogin() {
          if (
            usernameInput.value === "homeless" &&
            passwordInput.value === "homeless"
          ) {
            // Save login state with multiple methods for better mobile support
            localStorage.setItem("isLoggedIn", "true");
            sessionStorage.setItem("isLoggedIn", "true");
            // Also save with timestamp for verification
            const loginTime = new Date().getTime();
            localStorage.setItem("loginTime", loginTime.toString());
            sessionStorage.setItem("loginTime", loginTime.toString());

            showMainApp();
          } else {
            errorMessage.classList.remove("hidden");
            passwordInput.value = "";
            usernameInput.focus();
          }
        }

        function showMainApp() {
          loginContainer.classList.add("hidden");
          checklistContainer.classList.remove("hidden");
          document
            .querySelector(".main-container")
            .classList.remove("items-center", "justify-center");
        }

        function checkLoginState() {
          // Check multiple storage methods for better mobile compatibility
          const localLoggedIn = localStorage.getItem("isLoggedIn");
          const sessionLoggedIn = sessionStorage.getItem("isLoggedIn");
          const localLoginTime = localStorage.getItem("loginTime");
          const sessionLoginTime = sessionStorage.getItem("loginTime");

          // Consider logged in if any storage method confirms it
          const isLoggedIn =
            (localLoggedIn === "true" || sessionLoggedIn === "true") &&
            (localLoginTime || sessionLoginTime);

          console.log("Login check:", {
            localLoggedIn,
            sessionLoggedIn,
            localLoginTime,
            sessionLoginTime,
            isLoggedIn,
          });

          if (isLoggedIn) {
            // Ensure both storage methods are synchronized
            localStorage.setItem("isLoggedIn", "true");
            sessionStorage.setItem("isLoggedIn", "true");
            if (localLoginTime)
              sessionStorage.setItem("loginTime", localLoginTime);
            if (sessionLoginTime)
              localStorage.setItem("loginTime", sessionLoginTime);

            showMainApp();
            // Initialize data after showing the app
            initializeData();
          }
        }

        function logout() {
          // Clear login state from both storage methods
          localStorage.removeItem("isLoggedIn");
          localStorage.removeItem("loginTime");
          sessionStorage.removeItem("isLoggedIn");
          sessionStorage.removeItem("loginTime");

          // Show login form
          checklistContainer.classList.add("hidden");
          loginContainer.classList.remove("hidden");
          document
            .querySelector(".main-container")
            .classList.add("items-center", "justify-center");
          // Clear form
          usernameInput.value = "";
          passwordInput.value = "";
          errorMessage.classList.add("hidden");
          usernameInput.focus();
        }

        function createRowHTML(productName, notesPlaceholder = "") {
          return `
                    <td data-label="Product" class="px-2 py-2 font-medium text-slate-900" data-field="name">${productName}</td>
                    <td data-label="Qty" class="px-2 py-2"><input type="number" min="0" class="notes-input text-center" placeholder="0" data-field="quantity"></td>
                    <td data-label="Priority" class="px-2 py-2">
                        <select class="styled-select" data-field="priority">
                            <option value="Normal">Normal</option>
                            <option value="High">High</option>
                        </select>
                    </td>
                    <td data-label="Est. Cost" class="px-2 py-2"><input type="number" min="0" step="0.01" class="notes-input text-center" placeholder="0.00" data-field="cost"></td>
                    <td data-label="Needed By" class="px-2 py-2"><input type="date" class="notes-input" data-field="date"></td>
                    <td data-label="Bought" class="px-2 py-2 text-center"><input type="checkbox" class="form-checkbox" data-field="bought"></td>
                    <td data-label="Ready" class="px-2 py-2 text-center"><input type="checkbox" class="form-checkbox" data-field="ready"></td>
                    <td data-label="Donated" class="px-2 py-2 text-center"><input type="checkbox" class="form-checkbox" data-field="donated"></td>
                    <td data-label="Notes" class="px-2 py-2"><input type="text" class="notes-input" placeholder="${notesPlaceholder}" data-field="notes"></td>
                    <td data-label="Actions" class="px-2 py-2 text-center no-print">
                        <div class="flex items-center justify-center gap-2">
                            <button class="action-btn edit-btn">Edit</button>
                            <button class="action-btn delete-btn">Delete</button>
                        </div>
                    </td>
                `;
        }

        function addProduct(productName, tableBody, notesPlaceholder) {
          if (!productName.trim()) return;
          const newRow = tableBody.insertRow();
          newRow.className =
            "bg-white border-b last:border-b-0 hover:bg-slate-50";
          newRow.innerHTML = createRowHTML(productName, notesPlaceholder);
          updateAllProgressBars();
          updateAllCostTotals();
          saveData(); // Save data after adding product
        }

        function handleTableActions(e) {
          const target = e.target;
          const row = target.closest("tr");
          if (!row) return;

          if (
            target.matches('[data-field="donated"]') ||
            target.matches('input[type="checkbox"]') ||
            target.matches("input") ||
            target.matches("select")
          ) {
            updateAllProgressBars();
            updateAllCostTotals();
            saveData(); // Save data when any input changes
            return;
          }

          if (target.classList.contains("delete-btn")) {
            rowToDelete = row;
            deleteModal.classList.remove("hidden");
          } else if (target.classList.contains("edit-btn")) {
            const nameCell = row.querySelector('[data-field="name"]');
            const currentName = nameCell.textContent;
            nameCell.innerHTML = `<input type="text" class="notes-input" value="${currentName}">`;
            nameCell.querySelector("input").focus();
            target.textContent = "Save";
            target.classList.remove("edit-btn");
            target.classList.add("save-btn");
          } else if (target.classList.contains("save-btn")) {
            const nameCell = row.querySelector('[data-field="name"]');
            const input = nameCell.querySelector("input");
            nameCell.textContent = input.value;
            target.textContent = "Edit";
            target.classList.remove("save-btn");
            target.classList.add("edit-btn");
            saveData(); // Save data after editing product name
          }
        }

        function updateProgressBar(tableBodyId, progressBarId) {
          const tableBody = document.getElementById(tableBodyId);
          const progressBar = document.getElementById(progressBarId);
          if (!tableBody || !progressBar) return;

          const totalRows = tableBody.rows.length;
          if (totalRows === 0) {
            progressBar.style.width = "0%";
            progressBar.textContent = "0%";
            return;
          }
          const donatedRows = tableBody.querySelectorAll(
            'input[data-field="donated"]:checked'
          ).length;
          const percentage = Math.round((donatedRows / totalRows) * 100);

          progressBar.style.width = percentage + "%";
          progressBar.textContent = percentage + "%";
        }

        function updateAllProgressBars() {
          updateProgressBar("drm-table-body", "drm-progress");
          updateProgressBar("sh-table-body", "sh-progress");
          updateProgressBar("food-table-body", "food-progress");
        }

        // --- Cost Calculation Functions ---
        function formatCurrency(amount) {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(amount);
        }

        function calculateTableCost(tableBodyId) {
          const tableBody = document.getElementById(tableBodyId);
          if (!tableBody) return 0;

          let total = 0;
          for (let row of tableBody.rows) {
            const quantityInput = row.querySelector('[data-field="quantity"]');
            const costInput = row.querySelector('[data-field="cost"]');

            const quantity = parseFloat(quantityInput?.value || 0);
            const cost = parseFloat(costInput?.value || 0);

            // Calculate line total: quantity × cost
            const lineTotal = quantity * cost;
            if (!isNaN(lineTotal)) {
              total += lineTotal;
            }
          }
          return total;
        }

        function updateCostTotal(tableBodyId, costTotalId) {
          const total = calculateTableCost(tableBodyId);
          const costTotalElement = document.getElementById(costTotalId);
          if (costTotalElement) {
            costTotalElement.textContent = formatCurrency(total);
          }
          return total;
        }

        function updateAllCostTotals() {
          const drmTotal = updateCostTotal("drm-table-body", "drm-cost-total");
          const shTotal = updateCostTotal("sh-table-body", "sh-cost-total");
          const foodTotal = updateCostTotal(
            "food-table-body",
            "food-cost-total"
          );

          // Update grand total
          const grandTotal = drmTotal + shTotal + foodTotal;
          const grandTotalElement =
            document.getElementById("grand-total-amount");
          if (grandTotalElement) {
            grandTotalElement.textContent = formatCurrency(grandTotal);
          }
        }

        confirmDeleteBtn.addEventListener("click", () => {
          if (rowToDelete) {
            rowToDelete.remove();
            rowToDelete = null;
            updateAllProgressBars();
            updateAllCostTotals();
            saveData(); // Save data after deleting
          }
          deleteModal.classList.add("hidden");
        });

        cancelDeleteBtn.addEventListener("click", () => {
          rowToDelete = null;
          deleteModal.classList.add("hidden");
        });

        loginButton.addEventListener("click", attemptLogin);
        passwordInput.addEventListener(
          "keypress",
          (e) => e.key === "Enter" && attemptLogin()
        );
        usernameInput.addEventListener(
          "keypress",
          (e) => e.key === "Enter" && attemptLogin()
        );
        printBtn.addEventListener("click", () => window.print());
        logoutBtn.addEventListener("click", logout);

        document
          .getElementById("checklist-container")
          .addEventListener("click", handleTableActions);

        addProductDrmBtn.addEventListener("click", () => {
          addProduct(newProductDrmInput.value, drmTableBody);
          newProductDrmInput.value = "";
        });
        newProductDrmInput.addEventListener(
          "keypress",
          (e) => e.key === "Enter" && addProductDrmBtn.click()
        );

        addProductShBtn.addEventListener("click", () => {
          addProduct(newProductShInput.value, shTableBody);
          newProductShInput.value = "";
        });
        newProductShInput.addEventListener(
          "keypress",
          (e) => e.key === "Enter" && addProductShBtn.click()
        );

        addProductFoodBtn.addEventListener("click", () => {
          addProduct(newProductFoodInput.value, foodTableBody);
          newProductFoodInput.value = "";
        });
        newProductFoodInput.addEventListener(
          "keypress",
          (e) => e.key === "Enter" && addProductFoodBtn.click()
        );

        // Load saved data or initialize with default data
        async function initializeData() {
          const savedData = await loadData();
          if (savedData) {
            // Load from saved data
            if (savedData.drm && savedData.drm.length > 0) {
              populateTableFromData("drm-table-body", savedData.drm);
            } else {
              initialDrmProducts.forEach((p) => addProduct(p, drmTableBody));
            }

            if (savedData.sh && savedData.sh.length > 0) {
              populateTableFromData("sh-table-body", savedData.sh);
            } else {
              initialShProducts.forEach((p) => {
                let placeholder = "";
                if (p.includes("General Hygiene"))
                  placeholder = "e.g., combs, brushes";
                if (p.includes("Crossword Puzzles"))
                  placeholder = "Don't forget pens/pencils!";
                addProduct(p, shTableBody, placeholder);
              });
            }

            if (savedData.food && savedData.food.length > 0) {
              populateTableFromData("food-table-body", savedData.food);
            } else {
              initialFoodProducts.forEach((p) => addProduct(p, foodTableBody));
            }
          } else {
            // Initialize with default data
            initialDrmProducts.forEach((p) => addProduct(p, drmTableBody));
            initialShProducts.forEach((p) => {
              let placeholder = "";
              if (p.includes("General Hygiene"))
                placeholder = "e.g., combs, brushes";
              if (p.includes("Crossword Puzzles"))
                placeholder = "Don't forget pens/pencils!";
              addProduct(p, shTableBody, placeholder);
            });
            initialFoodProducts.forEach((p) => addProduct(p, foodTableBody));
          }
          updateAllProgressBars();
          updateAllCostTotals();
        }

        // Check login state on page load with mobile-friendly delay
        setTimeout(() => {
          checkLoginState();

          // Initialize data only if not logged in (logged in users get data initialized in checkLoginState)
          const localLoggedIn = localStorage.getItem("isLoggedIn");
          const sessionLoggedIn = sessionStorage.getItem("isLoggedIn");
          if (localLoggedIn !== "true" && sessionLoggedIn !== "true") {
            initializeData();
          }
        }, 100); // Small delay for mobile browsers to fully load localStorage
      });
    </script>
  </body>
</html>
